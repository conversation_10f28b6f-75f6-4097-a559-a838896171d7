import fs from 'fs';
import path from 'path';

// Function to extract all keys from a translation object
function extractKeys(obj, prefix = '') {
  const keys = [];
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value) && typeof value !== 'function') {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

// Function to load and parse a translation file
function loadTranslationFile(filePath) {
  try {
    // Read the file content
    const content = fs.readFileSync(filePath, 'utf8');

    // Remove the export default and extract the object
    const objectMatch = content.match(/export default\s*(\{[\s\S]*\})/);
    if (!objectMatch) {
      console.log(`Could not parse ${filePath}`);
      return null;
    }

    // Use eval to parse the object (not ideal but works for this script)
    const obj = eval(`(${objectMatch[1]})`);
    return obj;
  } catch (error) {
    console.log(`Error loading ${filePath}:`, error.message);
    return null;
  }
}

// Main function to check translations
function checkTranslations() {
  const localesDir = './i18n/locales';
  const languages = ['en', 'sk', 'de', 'pl'];
  const categories = [
    '', // root files
    'auth',
    'blog',
    'common',
    'errors',
    'faq',
    'footer',
    'info',
    'landing',
    'nav',
    'organiser',
    'promo_codes',
    'public'
  ];

  const missingTranslations = {};

  for (const category of categories) {
    const categoryPath = category ? path.join(localesDir, category) : localesDir;

    // Load English (reference) translations
    const enFile = category ? path.join(categoryPath, 'en.ts') : path.join(localesDir, 'en.ts');
    if (!fs.existsSync(enFile)) continue;

    const enTranslations = loadTranslationFile(enFile);
    if (!enTranslations) continue;

    const enKeys = extractKeys(enTranslations);

    // Check other languages
    for (const lang of languages) {
      if (lang === 'en') continue;

      const langFile = category ? path.join(categoryPath, `${lang}.ts`) : path.join(localesDir, `${lang}.ts`);
      if (!fs.existsSync(langFile)) {
        console.log(`Missing file: ${langFile}`);
        continue;
      }

      const langTranslations = loadTranslationFile(langFile);
      if (!langTranslations) continue;

      const langKeys = extractKeys(langTranslations);
      const missingKeys = enKeys.filter(key => !langKeys.includes(key));

      if (missingKeys.length > 0) {
        const fileKey = category ? `${category}/${lang}.ts` : `${lang}.ts`;
        missingTranslations[fileKey] = missingKeys;
      }
    }
  }

  // Output results
  console.log('\n=== MISSING TRANSLATIONS REPORT ===\n');

  if (Object.keys(missingTranslations).length === 0) {
    console.log('✅ All translations are complete!');
  } else {
    for (const [file, keys] of Object.entries(missingTranslations)) {
      console.log(`❌ ${file}:`);
      keys.forEach(key => console.log(`   - ${key}`));
      console.log('');
    }
  }
}

checkTranslations();
